import React, { useEffect, useRef } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Dimensions, ScrollView, StatusBar, Animated, Easing } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { FontAwesome, MaterialIcons, Ionicons } from '@expo/vector-icons';
import { useAppContext } from '@/context/AppContext';
import { LightTheme, DarkTheme } from '@/constants/Theme';
import { router } from 'expo-router';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';

const { width } = Dimensions.get('window');

export default function WelcomeOnboarding() {
  const { user, theme } = useAppContext();
  const insets = useSafeAreaInsets();
  const themeColors = theme === 'light' ? LightTheme : DarkTheme;



  // Animation references
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;
  const heroScaleAnim = useRef(new Animated.Value(0.9)).current;
  const stepsAnim = useRef(new Animated.Value(0)).current;
  const featuresAnim = useRef(new Animated.Value(0)).current;
  const buttonAnim = useRef(new Animated.Value(0)).current;

  const handleImportResume = () => {
    // In strict mode, redirect directly to job preferences
    router.replace('/(modals)/job-preferences?onboarding=true&required=true');
  };

  const handleStartJourney = () => {
    router.replace('/(modals)/job-preferences?onboarding=true&required=true');
  };

  const firstName = user?.name ? user.name.split(' ')[0] : 'there';

  // Start entrance animations
  useEffect(() => {
    const animations = [
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        delay: 200,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: true,
      }),
      Animated.timing(heroScaleAnim, {
        toValue: 1,
        duration: 1000,
        delay: 400,
        easing: Easing.out(Easing.back(1.2)),
        useNativeDriver: true,
      }),
      Animated.timing(stepsAnim, {
        toValue: 1,
        duration: 600,
        delay: 600,
        easing: Easing.out(Easing.quad),
        useNativeDriver: true,
      }),
      Animated.timing(featuresAnim, {
        toValue: 1,
        duration: 500,
        delay: 800,
        easing: Easing.out(Easing.quad),
        useNativeDriver: true,
      }),
      Animated.timing(buttonAnim, {
        toValue: 1,
        duration: 500,
        delay: 1000,
        easing: Easing.out(Easing.quad),
        useNativeDriver: true,
      }),
    ];

    Animated.parallel(animations).start();
  }, []);

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: themeColors.background }]}> 
      <StatusBar barStyle={theme === 'light' ? 'dark-content' : 'light-content'} />

      {/* Enhanced background gradient */}
      <LinearGradient
        colors={theme === 'light' ? ['#F8FAFC', '#E2E8F0', '#CBD5E1'] : ['#0F172A', '#1E293B', '#334155']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.backgroundGradient}
      />

      {/* Enhanced decorative blobs */}
      <LinearGradient
        colors={theme === 'light' ? ['#6366F1', '#8B5CF6', '#EC4899'] : ['#4F46E5', '#7C3AED', '#DB2777']}
        style={[styles.blob, styles.blobTopLeft, { top: Math.max(insets.top, 32) * -0.3 }]}
      />
      <LinearGradient
        colors={theme === 'light' ? ['#10B981', '#06B6D4', '#3B82F6'] : ['#059669', '#0891B2', '#2563EB']}
        style={[styles.blob, styles.blobBottomRight]}
      />

      <ScrollView 
        contentContainerStyle={[styles.scrollContent, { paddingTop: Math.max(insets.top + 10, 20) }]}
        showsVerticalScrollIndicator={false}
      >
        {/* Enhanced Hero card with animations */}
        <Animated.View 
          style={[
            styles.heroWrapper,
            {
              opacity: fadeAnim,
              transform: [
                { translateY: slideAnim },
                { scale: heroScaleAnim }
              ]
            }
          ]}
        >
          <BlurView intensity={theme === 'light' ? 20 : 80} tint={theme === 'light' ? 'light' : 'dark'} style={[styles.heroCard, { borderColor: themeColors.border }]}> 
            {/* Enhanced badge row */}
            <View style={styles.badgeRow}>
              <View style={[styles.badge, { backgroundColor: theme === 'light' ? 'rgba(99,102,241,0.15)' : 'rgba(99,102,241,0.25)', borderColor: theme === 'light' ? 'rgba(99,102,241,0.4)' : 'rgba(99,102,241,0.5)' }]}>
                <Ionicons name="sparkles" size={14} color={theme === 'light' ? '#6366F1' : '#8B9DFF'} />
                <Text style={[styles.badgeText, { color: theme === 'light' ? '#4F46E5' : '#A5B4FC' }]}>AI-Powered</Text>
              </View>
              <View style={[styles.badge, { backgroundColor: theme === 'light' ? 'rgba(16,185,129,0.15)' : 'rgba(16,185,129,0.25)', borderColor: theme === 'light' ? 'rgba(16,185,129,0.4)' : 'rgba(16,185,129,0.5)' }]}>
                <Ionicons name="checkmark-circle" size={14} color="#10B981" />
                <Text style={[styles.badgeText, { color: '#10B981' }]}>2-min setup</Text>
              </View>
              <View style={[styles.badge, { backgroundColor: theme === 'light' ? 'rgba(245,158,11,0.15)' : 'rgba(245,158,11,0.25)', borderColor: theme === 'light' ? 'rgba(245,158,11,0.4)' : 'rgba(245,158,11,0.5)' }]}>
                <Ionicons name="shield-checkmark" size={14} color={theme === 'light' ? '#F59E0B' : '#FBBF24'} />
                <Text style={[styles.badgeText, { color: theme === 'light' ? '#F59E0B' : '#FBBF24' }]}>Secure</Text>
              </View>
            </View>

            {/* Enhanced welcome message */}
            <View style={styles.welcomeSection}>
              <Text style={[styles.welcomeLabel, { color: themeColors.textSecondary }]}>Welcome back,</Text>
              <Text style={[styles.title, { color: themeColors.text }]}>
                {firstName}! 👋
              </Text>
            </View>
            
            <Text style={[styles.subtitle, { color: themeColors.textSecondary }]}>
              Ready to find your dream job? Let's personalize your experience with smart recommendations tailored just for you.
            </Text>

            {/* Enhanced CTA section */}
            <View style={styles.ctaRow}>
              <TouchableOpacity style={[styles.secondaryCta, { borderColor: themeColors.border, backgroundColor: theme === 'light' ? 'rgba(255,255,255,0.8)' : 'rgba(255,255,255,0.1)', flex: 1, marginRight: 8 }]} onPress={handleImportResume} activeOpacity={0.8}>
                <Ionicons name="document-text" size={16} color={themeColors.text} />
                <Text style={[styles.secondaryCtaText, { color: themeColors.text }]}>Import Resume</Text>
              </TouchableOpacity>
              <TouchableOpacity style={[styles.primaryCta, { backgroundColor: themeColors.tint, flex: 1, marginLeft: 8 }]} onPress={handleStartJourney} activeOpacity={0.8}>
                <Ionicons name="rocket" size={16} color="#fff" />
                <Text style={[styles.primaryCtaText, { color: '#fff' }]}>Start Journey</Text>
              </TouchableOpacity>
            </View>
          </BlurView>
        </Animated.View>

        {/* Enhanced Steps with animations */}
        <Animated.View 
          style={[
            styles.stepsContainer,
            {
              opacity: stepsAnim,
              transform: [{
                translateY: stepsAnim.interpolate({
                  inputRange: [0, 1],
                  outputRange: [30, 0]
                })
              }]
            }
          ]}
        >
          <StepCard
            themeMode={theme}
            titleColor={themeColors.text}
            subtitleColor={themeColors.textSecondary}
            bgColor={themeColors.card}
            borderColor={themeColors.border}
            iconBg={theme === 'light' ? 'rgba(99,102,241,0.12)' : 'rgba(99,102,241,0.2)'}
            icon={<Ionicons name="settings" size={24} color={theme === 'light' ? '#6366F1' : '#A5B4FC'} />}
            title="Set Your Preferences"
            description="Tell us about your ideal roles, preferred locations, and salary expectations."
          />
          <StepCard
            themeMode={theme}
            titleColor={themeColors.text}
            subtitleColor={themeColors.textSecondary}
            bgColor={themeColors.card}
            borderColor={themeColors.border}
            iconBg={theme === 'light' ? 'rgba(16,185,129,0.12)' : 'rgba(16,185,129,0.2)'}
            icon={<Ionicons name="analytics" size={24} color={theme === 'light' ? '#10B981' : '#34D399'} />}
            title="Get Smart Matches"
            description="Receive personalized job recommendations ranked by compatibility and potential."
          />
          <StepCard
            themeMode={theme}
            titleColor={themeColors.text}
            subtitleColor={themeColors.textSecondary}
            bgColor={themeColors.card}
            borderColor={themeColors.border}
            iconBg={theme === 'light' ? 'rgba(245,158,11,0.12)' : 'rgba(245,158,11,0.2)'}
            icon={<Ionicons name="send" size={24} color={theme === 'light' ? '#F59E0B' : '#FBBF24'} />}
            title="Apply with Confidence"
            description="Use AI-powered insights, custom cover letters, and application tracking."
          />
        </Animated.View>

        {/* Enhanced Feature highlights */}
        <Animated.View 
          style={[
            styles.featuresContainer,
            {
              opacity: featuresAnim,
              transform: [{
                translateY: featuresAnim.interpolate({
                  inputRange: [0, 1],
                  outputRange: [20, 0]
                })
              }]
            }
          ]}
        >
          <FeaturePill icon="flash" text="Lightning Fast" color={theme === 'light' ? '#F59E0B' : '#FBBF24'} />
          <FeaturePill icon="shield" text="Privacy First" color={theme === 'light' ? '#10B981' : '#34D399'} />
          <FeaturePill icon="star" text="Smart Matching" color={theme === 'light' ? '#6366F1' : '#A5B4FC'} />
          <FeaturePill icon="trending-up" text="Career Growth" color={theme === 'light' ? '#8B5CF6' : '#C084FC'} />
        </Animated.View>

        {/* Enhanced Bottom start button */}
        <Animated.View
          style={[
            {
              opacity: buttonAnim,
              transform: [{
                translateY: buttonAnim.interpolate({
                  inputRange: [0, 1],
                  outputRange: [20, 0]
                })
              }]
            }
          ]}
        >
          <TouchableOpacity style={[styles.bottomStartBtn, { backgroundColor: theme === 'light' ? '#000' : '#2563EB', marginTop: 32 }]} onPress={handleStartJourney} activeOpacity={0.9}>
            <Text style={styles.bottomStartText}>Start Your Journey</Text>
            <Ionicons name="arrow-forward" size={20} color="#fff" />
          </TouchableOpacity>
        </Animated.View>

        <View style={{ height: Math.max(50, insets.bottom) }} />
      </ScrollView>
    </SafeAreaView>
  );
}

const StepCard = ({ icon, title, description, themeMode, bgColor, borderColor, titleColor, subtitleColor, iconBg }: { 
  icon: React.ReactNode;
  title: string;
  description: string;
  themeMode: 'light' | 'dark';
  bgColor: string;
  borderColor: string;
  titleColor: string;
  subtitleColor: string;
  iconBg: string;
}) => {
  return (
    <View style={[styles.stepCard, { backgroundColor: bgColor, borderColor }]}>
      <View style={[styles.stepIcon, { backgroundColor: iconBg }]}>
        {icon}
      </View>
      <View style={styles.stepContent}>
        <Text style={[styles.stepTitle, { color: titleColor }]}>{title}</Text>
        <Text style={[styles.stepDescription, { color: subtitleColor }]}>{description}</Text>
      </View>
    </View>
  );
};

const FeaturePill = ({ icon, text, color }: { icon: string; text: string; color: string }) => (
  <View style={[styles.featurePill, { borderColor: color, backgroundColor: `${color}20` }]}> 
    <Ionicons name={icon as any} size={14} color={color} />
    <Text style={[styles.featureText, { color }]}>{text}</Text>
  </View>
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backgroundGradient: {
    ...StyleSheet.absoluteFillObject,
  },
  scrollContent: {
    paddingBottom: 20,
  },
  blob: {
    position: 'absolute',
    width: width * 0.9,
    height: width * 0.9,
    borderRadius: width * 0.45,
    opacity: 0.15,
  },
  blobTopLeft: {
    left: -width * 0.3,
  },
  blobBottomRight: {
    bottom: -width * 0.4,
    right: -width * 0.3,
  },
  heroWrapper: {
    paddingHorizontal: 20,
    marginBottom: 8,
  },
  heroCard: {
    borderRadius: 28,
    padding: 14,
    overflow: 'hidden',
    borderWidth: 1,
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowRadius: 20,
    elevation: 8,
    marginHorizontal: 11,
    marginBottom: 17.6,
  },
  badgeRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 10,
    marginBottom: 16,
  },
  badge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 999,
    borderWidth: 1,
  },
  badgeText: {
    fontSize: 12,
    fontWeight: '700',
  },
  welcomeSection: {
    marginBottom: 8,
  },
  welcomeLabel: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  title: {
    fontSize: 36,
    fontWeight: '900',
    letterSpacing: -1,
    lineHeight: 42,
  },
  subtitle: {
    fontSize: 17,
    lineHeight: 24,
    marginTop: 12,
    opacity: 0.9,
  },
  ctaRow: {
    flexDirection: 'row',
    gap: 14,
    marginTop: 24,
  },
  primaryCta: {
    paddingHorizontal: 16,
    paddingVertical: 18,
    borderRadius: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    minHeight: 56,
    shadowColor: '#6366F1',
    shadowOpacity: 0.3,
    shadowRadius: 10,
    elevation: 5,
  },
  primaryCtaGradient: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      gap: 10,
      paddingVertical: 20,
      paddingHorizontal: 12,
      borderRadius: 16,
      minHeight: 56,
    },
  primaryCtaText: {
    fontWeight: '700',
    fontSize: 15,
  },
  secondaryCta: {
      paddingHorizontal: 16,
      paddingVertical: 18,
      borderRadius: 16,
      borderWidth: 1.5,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      gap: 8,
      minHeight: 56,
    },
  secondaryCtaText: {
    fontWeight: '700',
    fontSize: 15,
  },
  stepsContainer: {
    paddingHorizontal: 20,
    marginTop: 32,
    gap: 16,
  },
  stepCard: {
    flexDirection: 'row',
    borderRadius: 20,
    padding: 20,
    borderWidth: 1,
    shadowColor: '#000',
    shadowOpacity: 0.08,
    shadowRadius: 15,
    elevation: 4,
  },
  stepIcon: {
    width: 48,
    height: 48,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  stepContent: {
    flex: 1,
  },
  stepTitle: {
    fontSize: 18,
    fontWeight: '800',
    marginBottom: 6,
  },
  stepDescription: {
    fontSize: 15,
    lineHeight: 21,
  },
  featuresContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    paddingHorizontal: 20,
    marginTop: 28,
    justifyContent: 'center',
  },
  featurePill: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    borderWidth: 1.5,
    borderRadius: 999,
    paddingVertical: 10,
    paddingHorizontal: 16,
  },
  featureText: {
    fontSize: 13,
    fontWeight: '700',
  },
  bottomStartBtn: {
    marginHorizontal: 20,
    borderRadius: 20,
    paddingVertical: 18,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    gap: 10,
    shadowColor: '#000',
    shadowOpacity: 0.2,
    shadowRadius: 15,
    elevation: 6,
  },
  bottomStartText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '900',
  },
});
