import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { Appearance, ColorSchemeName, useColorScheme as useNativeColorScheme } from 'react-native';
import { supabase } from '@/lib/supabase';
import { clearUserApplicationData } from '@/lib/secureStorage';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { saveJobApplication, getUserApplications } from '@/services/supabaseApplicationService';

// Helper function to validate if a job ID exists in the database
async function validateJobExists(jobId: string): Promise<boolean> {
  try {
    const { data, error } = await supabase
      .from('jobs')
      .select('id')
      .eq('id', jobId)
      .single();

    if (error) {
      console.log(`[AppContext] Job ${jobId} does not exist in database:`, error.message);
      return false;
    }

    return !!data;
  } catch (error) {
    console.error(`[AppContext] Error validating job existence for ${jobId}:`, error);
    return false;
  }
}

// Define our application types
export interface Job {
  id: string;
  title: string;
  company: string;
  location: string;
  pay: string;
  image: string;
  distance: string;
  tags: string[];
  description: string;
  qualifications: string[];
  requirements: string[];
  logo?: string;
  url?: string;
  postedDate?: string; // ISO string date for time-based filtering
}

// Unified AppliedJob type for context and UI
export interface AppliedJob {
  job: Job;
  status: 'pending' | 'accepted' | 'denied' | 'responded' | 'applying';
  statusColor?: string; // Added color for status
  responseMessage?: string;
  appliedAt?: string;
  respondedAt?: string;
}

export interface User {
  id: string;
  name: string;
  email: string;
  avatar: string;
  skills: string[];
  experience: string[];
  userType?: 'job_seeker' | 'service_provider';
  onboardingCompleted?: boolean;
}

// Context type definition
interface AppContextType {
  // Theme
  theme: 'light' | 'dark';
  toggleTheme: () => void;
  // Job applications
  applications: AppliedJob[];
  addApplication: (job: Job) => void;
  updateApplication: (jobId: string, update: Partial<AppliedJob>) => void;
  // User profile
  user: User | null;
  setUser: (user: User | null) => void;
  isLoading: boolean;
  // Auth
  signOut: () => Promise<void>;
  refreshUserProfile: () => Promise<void>;
}

// Create the context with default values
const AppContext = createContext<AppContextType>({
  theme: 'light',
  toggleTheme: () => {},
  applications: [],
  addApplication: () => {},
  updateApplication: () => {},
  user: null,
  setUser: () => {},
  isLoading: false,
  signOut: async () => {},
  refreshUserProfile: async () => {},
});

// Custom hook to use the app context
export const useAppContext = () => useContext(AppContext);

// Safe version of the context hook that prevents insertion effect errors
export const useSafeAppContext = () => {
  // Create a ref to store initial context values
  const contextRef = React.useRef<AppContextType | null>(null);
  
  // Initialize the ref if needed - this prevents insertion effect issues
  if (contextRef.current === null) {
    // Get initial values - this only runs once during first render
    contextRef.current = useContext(AppContext);
  }
  
  // Get the current context (this might trigger warnings in strict mode)
  const context = useContext(AppContext);
  
  // Update ref with latest values after render
  React.useEffect(() => {
    contextRef.current = context;
  }, [context]);
  
  // Return the context from the ref during render
  return contextRef.current;
};

export const AppProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Get system color scheme and track changes
  const systemColorScheme = useNativeColorScheme() as 'light' | 'dark';
  
  // Initialize theme with persistent storage
  const [theme, setTheme] = useState<'light' | 'dark'>(systemColorScheme || 'light');
  const [themePreference, setThemePreference] = useState<'system' | 'light' | 'dark'>('system');
  
  // Helper function to synchronize theme state across the app
  const synchronizeThemeState = useCallback(async (newPreference: 'system' | 'light' | 'dark') => {
    console.log(`[AppContext] Synchronizing theme state with preference: ${newPreference}`);
    
    // Update the preference state
    setThemePreference(newPreference);
    
    // Determine the actual theme to apply
    let themeToApply: 'light' | 'dark';
    
    if (newPreference === 'system') {
      // Use the system's current theme
      themeToApply = systemColorScheme || 'light';
      console.log(`[AppContext] Using system theme: ${themeToApply}`);
    } else {
      // Use the explicit user preference
      themeToApply = newPreference as 'light' | 'dark';
      console.log(`[AppContext] Using explicit theme: ${themeToApply}`);
    }
    
    // Apply the theme
    setTheme(themeToApply);
    
    // Save to AsyncStorage
    try {
      await AsyncStorage.setItem('theme_preference', newPreference);
      console.log(`[AppContext] Saved theme preference: ${newPreference}`);
    } catch (error) {
      console.error('[AppContext] Error saving theme preference:', error);
    }
  }, [systemColorScheme]);
  
  // Effect for loading saved theme preference from storage on app start
  useEffect(() => {
    const loadThemePreference = async () => {
      try {
        const savedThemePreference = await AsyncStorage.getItem('theme_preference');

        if (savedThemePreference) {
          // Use our synchronization helper to properly set everything up
          await synchronizeThemeState(savedThemePreference as 'system' | 'light' | 'dark');
          console.log(`[AppContext] Loaded and applied theme preference: ${savedThemePreference}`);
        } else {
          // First time app run, default to system and save this preference
          await synchronizeThemeState('system');
          console.log('[AppContext] No stored theme preference found, defaulted to system');
        }
      } catch (error) {
        console.error('[AppContext] Error loading theme preference:', error);
        // Fallback to system theme in case of error
        synchronizeThemeState('system');
      }
    };

    // Defer theme loading to prevent insertion effect warnings
    requestAnimationFrame(() => {
      loadThemePreference();
    });
  }, [synchronizeThemeState]);
  
  // Initialize user data
  const [user, setUser] = useState<User | null>(null);
  
  // Loading state
  const [isLoading, setIsLoading] = useState(false);
  
  // Applications state as AppliedJob[]
  const [applications, setApplications] = useState<AppliedJob[]>([]);

  // Load applications from storage when user changes
  useEffect(() => {
    const loadApplications = async () => {
      try {
        if (!user?.id) {
          // No user logged in, clear applications
          console.log('[AppContext] No user logged in, clearing applications');
          setApplications([]);
          return;
        }

        // Load user-specific applications
        const userSpecificKey = `user_applications_${user.id}`;
        const storedApplications = await AsyncStorage.getItem(userSpecificKey);
        if (storedApplications) {
          const parsedApplications = JSON.parse(storedApplications);
          console.log(`[AppContext] Loaded ${parsedApplications.length} applications from storage for user ${user.id}`);
          setApplications(parsedApplications);
        } else {
          console.log(`[AppContext] No stored applications found for user ${user.id}`);
          setApplications([]);
        }
      } catch (error) {
        console.error('Failed to load applications from storage:', error);
        setApplications([]);
      }
    };

    loadApplications();
  }, [user?.id]); // Depend on user.id so it reloads when user changes

  // Note: Applications are now stored in Supabase via supabaseApplicationService
  // This local state is for UI reactivity only // Depend on both applications and user.id

  // Properly handle system theme changes - only apply if user has selected 'system' preference
  useEffect(() => {
    const subscription = Appearance.addChangeListener(({ colorScheme }) => {
      if (colorScheme && themePreference === 'system') {
        console.log(`[AppContext] System theme changed to ${colorScheme}, applying because preference is 'system'`);
        // Defer theme update to prevent insertion effect warnings
        requestAnimationFrame(() => {
          setTheme(colorScheme as 'light' | 'dark');
        });
      } else if (colorScheme) {
        console.log(`[AppContext] System theme changed to ${colorScheme}, ignoring because preference is '${themePreference}'`);
      }
    });
    return () => subscription.remove();
  }, [themePreference]);

  // Effect to respond to system theme changes (only when preference is 'system')
  useEffect(() => {
    // Only update if preference is set to follow system
    if (themePreference === 'system' && systemColorScheme) {
      console.log(`[AppContext] System theme changed to ${systemColorScheme}, updating app theme`);
      // Defer theme update to prevent insertion effect warnings
      requestAnimationFrame(() => {
        setTheme(systemColorScheme);
      });
    }
  }, [systemColorScheme, themePreference]);

  // Improved toggle theme function that cycles through options
  const toggleTheme = useCallback(() => {
    // Get next theme in the cycle: light -> dark -> system -> light
    const getNextTheme = (current: 'light' | 'dark' | 'system'): 'light' | 'dark' | 'system' => {
      switch (current) {
        case 'light': return 'dark';
        case 'dark': return 'system';
        case 'system': return 'light';
        default: return 'light'; // Failsafe
      }
    };
    
    // Get the next theme preference in our cycle
    const nextThemePreference = getNextTheme(themePreference);
    console.log(`[AppContext] Toggling theme from ${themePreference} to ${nextThemePreference}`);
    
    // Use our synchronization helper to update everything consistently
    synchronizeThemeState(nextThemePreference);
  }, [themePreference, synchronizeThemeState]);

  // Add or update application - memoized to avoid recreating on every render
  const addApplication = useCallback((job: Job) => {
    setApplications(prev => {
      // If already applied, don't add again
      if (prev.some(app => app.job.id === job.id)) return prev;
      
      console.log(`[AppContext] Adding application for job: ${job.id} (${job.title})`);
      
      const newApplications = [
        {
          job,
          status: 'applying' as const,
          statusColor: '#FFC107',
          appliedAt: new Date().toISOString(),
        },
        ...prev,
      ];
      
      // Immediately persist to AsyncStorage (user-specific)
      (async () => {
        try {
          if (!user?.id) {
            console.log('[AppContext] No user logged in, skipping immediate save');
            return;
          }
          const userSpecificKey = `user_applications_${user.id}`;
          console.log(`[AppContext] Saving ${newApplications.length} applications to storage for user ${user.id}`);
          await AsyncStorage.setItem(userSpecificKey, JSON.stringify(newApplications));
          
          // Also save to Supabase if user is logged in
          if (user?.id) {
            console.log(`[AppContext] User is logged in, saving to database: ${job.id}`);
            
            try {
              // Prevent applications to mock jobs
              if (job.id.startsWith('mock-job-')) {
                console.error(`[AppContext] Cannot create application: Job ${job.id} is a mock job and doesn't exist in database`);
                return;
              }

              // Validate that the job exists in the database before creating an application
              const jobExists = await validateJobExists(job.id);
              if (!jobExists) {
                console.error(`[AppContext] Cannot create application: Job ${job.id} does not exist in database`);
                return;
              }

              console.log(`[AppContext] Job ${job.id} validated successfully`);

              // Get the current authenticated user ID from Supabase
              const { data: { user: authUser } } = await supabase.auth.getUser();
              const authUserId = authUser?.id;

              if (!authUserId) {
                console.error('[AppContext] No authenticated user found');
                return;
              }

              console.log(`[AppContext] Using auth user ID: ${authUserId} for database operations`);

              // Create application with the validated job ID
              const { error } = await supabase
                .from('matches')
                .upsert(
                  {
                    profile_id: authUserId,
                    job_id: job.id, // Use the original job ID since it's already validated
                    job_title: job.title,
                    job_company: job.company,
                    status: 'applying',
                    created_at: new Date().toISOString()
                  },
                  { onConflict: 'profile_id,job_id' }
                );

              if (error) {
                console.error('[AppContext] Failed to save application to database:', error);
              } else {
                console.log(`[AppContext] Successfully saved application for job ${job.id} to database`);
              }
            } catch (dbError) {
              console.error('[AppContext] Exception saving application to database:', dbError);
            }
          }
        } catch (error) {
          console.error('[AppContext] Failed to save applications to storage:', error);
        }
      })();
      
      return newApplications;
    });
  }, [user]);

  // Update application status/fields - memoized to avoid recreating on every render
  const updateApplication = useCallback((jobId: string, update: Partial<AppliedJob>) => {
    setApplications(prev => {
      const updatedApplications = prev.map(app =>
        app.job.id === jobId ? { ...app, ...update } : app
      );
      
      // Immediately persist to AsyncStorage (user-specific)
      (async () => {
        try {
          if (!user?.id) {
            console.log('[AppContext] No user logged in, skipping update save');
            return;
          }
          const userSpecificKey = `user_applications_${user.id}`;
          console.log(`[AppContext] Updating application ${jobId} and saving to storage for user ${user.id}`);
          await AsyncStorage.setItem(userSpecificKey, JSON.stringify(updatedApplications));
        } catch (error) {
          console.error('Failed to save updated applications to storage:', error);
        }
      })();
      
      return updatedApplications;
    });
  }, [user]);

  // Sign out function
  const signOut = useCallback(async () => {
    setIsLoading(true);
    try {
      // Clear applications before signing out
      console.log('[AppContext] Clearing applications on sign out');
      setApplications([]);

      // Clear all user application data from storage
      await clearUserApplicationData();

      await supabase.auth.signOut();
      setUser(null);
    } catch (error) {
      console.error('Error signing out:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Refresh user profile from database
  const refreshUserProfile = useCallback(async () => {
    if (!user?.id) return;
    
    setIsLoading(true);
    try {
      // Get the user's profile
      let { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .maybeSingle();  // Use maybeSingle instead of single to avoid errors when no record exists
        
      if (profileError) throw profileError;
      
      // If profile doesn't exist, create it with default values
      if (!profileData) {
        const userType = 'job_seeker'; // Default to job seeker
        
        // Create a new profile
        const { data: newProfile, error: insertError } = await supabase
          .from('profiles')
          .insert({
            id: user.id,
            name: user.name || 'New User',
            email: user.email || '',
            user_type: userType
          })
          .select('*')
          .single();
          
        if (insertError) throw insertError;
        profileData = newProfile;
        
        // Also create the job_seeker_profile record
        const { error: jobSeekerInsertError } = await supabase
          .from('job_seeker_profiles')
          .insert({
            profile_id: user.id,
            title: 'Job Seeker',
            bio: '',
          });
          
        if (jobSeekerInsertError) throw jobSeekerInsertError;
      }
      
      // Get user type specific data
      if (profileData.user_type === 'job_seeker') {
        // First check if job seeker profile exists
        let { data: jobSeekerData, error: jobSeekerError } = await supabase
          .from('job_seeker_profiles')
          .select('*')
          .eq('profile_id', user.id)
          .maybeSingle();  // Use maybeSingle instead of single
          
        if (jobSeekerError) throw jobSeekerError;
        
        // If job seeker profile doesn't exist, create it
        if (!jobSeekerData) {
          const { data: newJobSeekerData, error: createError } = await supabase
            .from('job_seeker_profiles')
            .insert({
              profile_id: user.id,
              title: 'Job Seeker',
              bio: '',
            })
            .select('*')
            .single();
            
          if (createError) throw createError;
          
          // Use the new data
          jobSeekerData = newJobSeekerData;
        }
        
        // Get skills - SECURITY: Always filter by current user
        const { data: skillsData, error: skillsError } = await supabase
          .from('user_skills')
          .select('skill_name')
          .eq('profile_id', user.id);

        if (skillsError) {
          console.error('Error fetching user skills:', skillsError);
          // Don't throw - continue with empty skills
        }

        // Get experiences - SECURITY: Always filter by current user
        const { data: experiencesData, error: experiencesError } = await supabase
          .from('experiences')
          .select('title, company')
          .eq('profile_id', user.id);

        if (experiencesError) {
          console.error('Error fetching user experiences:', experiencesError);
          // Don't throw - continue with empty experiences
        }
        
        // Update user state
        setUser({
          ...user,
          name: profileData.name || user.name,
          email: profileData.email || user.email,
          avatar: jobSeekerData?.photo_url || user.avatar,
          skills: skillsData?.map(skill => skill.skill_name) || [],
          experience: experiencesData?.map(exp => `${exp.title} at ${exp.company}`) || [],
          userType: 'job_seeker',
          onboardingCompleted: profileData.onboarding_completed || false,
        });
      } else if (profileData.user_type === 'service_provider') {
        // First check if service provider profile exists
        let { data: providerData, error: providerError } = await supabase
          .from('service_provider_profiles')
          .select('*')
          .eq('profile_id', user.id)
          .maybeSingle();  // Use maybeSingle instead of single
          
        if (providerError) throw providerError;
        
        // If provider profile doesn't exist, create it
        if (!providerData) {
          const { data: newProviderData, error: createError } = await supabase
            .from('service_provider_profiles')
            .insert({
              profile_id: user.id,
              name: profileData.name,
              service_categories: [],
            })
            .select('*')
            .single();
            
          if (createError) throw createError;
          
          // Use the new data
          providerData = newProviderData;
        }
        
        // Update user state
        setUser({
          ...user,
          name: profileData.name || user.name,
          email: profileData.email || user.email,
          avatar: providerData?.photo_url || user.avatar,
          skills: providerData?.service_categories || [],
          experience: [],
          userType: 'service_provider',
          onboardingCompleted: profileData.onboarding_completed || false,
        });
      }
    } catch (error) {
      console.error('Error refreshing user profile:', error);
    } finally {
      setIsLoading(false);
    }
  }, [user]);

  // Create stable context value with useMemo to prevent unnecessary re-renders
  const contextValue = React.useMemo(() => ({
    theme,
    toggleTheme,
    applications,
    addApplication,
    updateApplication,
    user,
    setUser,
    isLoading,
    signOut,
    refreshUserProfile,
  }), [
    theme, 
    toggleTheme, 
    applications, 
    addApplication, 
    updateApplication, 
    user, 
    isLoading, 
    signOut, 
    refreshUserProfile
  ]);

  // Provide the context value
  return (
    <AppContext.Provider value={contextValue}>
      {children}
    </AppContext.Provider>
  );
};
